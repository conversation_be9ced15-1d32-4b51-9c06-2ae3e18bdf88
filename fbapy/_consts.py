class CONSTS:
    class COLORS:
        C1 = "401372137331149"
        C2 = "276148839666236"
        C3 = "2163607613910521"
        C4 = "525779004528357"
        C5 = "1009318275928628"
        C6 = "452508575242041"
        C7 = "277332376527537"
        C8 = "241165263466288"
        C9 = "459530701251156"
        C10 = "2173297922999264"
        C11 = "367314917184744"
        C12 = "316152702351373"
        C13 = "1793841914061298"
        C14 = "2349018378676171"
        C15 = "236995927208996"
        C16 = "554617635055752"
        C17 = "410811529670314"
        C18 = "2013736355407001"
        C19 = "1858565907563681"
        C20 = "474022893007699"
        C21 = "464786114026686"
        C22 = "236819723594098"
        C23 = "314741219075524"
        C24 = "456542378188554"
        C25 = "338912070167253"
        C26 = "768161006877937"
        C27 = "856609964679094"
        C28 = "1187607261408676"
        C29 = "299352527433933"
        C30 = "280807115950091"

    class FONTS:
        SIMPLE = "233490655168261"
        CLEAN = "1191814831024887"
        CASUAL = "516266749248495"
        FANCY = "2133975226905828"
        HEADLINE = "1919119914775364"

    LIST_COLORS = [
        COLORS.C1,
        COLORS.C2,
        COLORS.C3,
        COLORS.C4,
        COLORS.C5,
        COLORS.C6,
        COLORS.C7,
        COLORS.C8,
        COLORS.C9,
        COLORS.C10,
        COLORS.C11,
        COLORS.C12,
        COLORS.C13,
        COLORS.C14,
        COLORS.C15,
        COLORS.C16,
        COLORS.C17,
        COLORS.C18,
        COLORS.C19,
        COLORS.C20,
        COLORS.C21,
        COLORS.C22,
        COLORS.C23,
        COLORS.C24,
        COLORS.C25,
        COLORS.C26,
        COLORS.C27,
        COLORS.C28,
        COLORS.C29,
        COLORS.C30,
    ]

    LIST_FONTS = [
        FONTS.SIMPLE,
        FONTS.CLEAN,
        FONTS.CASUAL,
        FONTS.FANCY,
        FONTS.HEADLINE,
    ]

    LIST_FONTS_KEYS = [
        "simple",
        "clean",
        "casual",
        "fancy",
        "headline",
    ]

    class EVENTS:
        MESSAGE = "message"
        MESSAGE_REPLY = "message_reply"
        MESSAGE_REACTION = "message_reaction"
        MESSAGE_UNSEND = "message_unsend"
        EVENT = "event"
        TYP = "typ"
        PRESENCE = "presence"
        READ_RECEIPT = "read_receipt"

    class LOG_MESSAGE:
        SUBSCRIBE = "log:subscribe"
        UNSUBSCRIBE = "log:unsubscribe"
        THEME =  "log:thread-color"
        ICON = "log:thread-icon"
        NICKNAME = "log:user-nickname"
        ADMINS = "log:thread-admins"
        POLL = "log:thread-poll"
        APPROVAL_MODE = "log:thread-approval-mode"
        CALL = "log:thread-call"
        NAME = "log:thread-name"
        IMAGE = "log:thread-image"
        PINNED = "log:thread-pinned-message"
    
