#!/usr/bin/env python3
"""
Simple test to verify fbapy login and basic functionality
"""

import os
from os.path import join, dirname
from dotenv import load_dotenv
from fbapy import *

# Load environment variables
dotenv_path = join(dirname(__file__), ".env")
load_dotenv(dotenv_path)

def main():
    print("Testing fbapy login...")
    
    # Initialize client
    client = Client()
    
    # Login
    api = client.login(
        appstate=os.environ.get("APPSTATE"),
        options={
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        },
    )
    
    if api:
        print("✅ Login successful!")
        print("✅ HTTP APIs are available for sending messages")
        print("❌ MQTT listening is broken (Facebook infrastructure change)")
        print("\nYou can now use:")
        print("- api.http.send_message('text', 'thread_id')")
        print("- api.http.get_user_info('user_id')")
        print("- api.graphql.share_story('text')")
        print("- And other HTTP/GraphQL functions")
    else:
        print("❌ Login failed")

if __name__ == "__main__":
    main()
