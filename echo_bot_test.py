#!/usr/bin/env python3
"""
Echo Bot Test - Tests the Facebook API library with a simple echo bot
"""

import os
from os.path import join, dirname
from dotenv import load_dotenv
from fbapy import *

# Load environment variables
dotenv_path = join(dirname(__file__), ".env")
load_dotenv(dotenv_path)

def callback(event, api):
    """
    Callback function for handling incoming messages
    """
    print(f"📨 Received event: {event}")
    
    if event.get("type") == "message":
        thread_id = event.get("thread_id")
        message_body = event.get("body", "")
        sender_id = event.get("sender_id")
        
        print(f"💬 Message from {sender_id} in {thread_id}: {message_body}")
        
        # Echo the message back
        if message_body.strip():
            echo_message = f"🔄 Echo: {message_body}"
            try:
                # Try using HTTP send_message
                api.http.send_message(echo_message, thread_id)
                print(f"✅ Echoed message successfully")
            except Exception as e:
                print(f"❌ Failed to echo message: {e}")
                
                # Try alternative send_message
                try:
                    api.send_message(text=echo_message, thread_id=thread_id)
                    print(f"✅ Echoed message with alternative method")
                except Exception as e2:
                    print(f"❌ Alternative send_message also failed: {e2}")

def main():
    print("🤖 Starting Echo Bot Test...")
    
    # Initialize client
    client = Client()
    
    # Login
    print("🔐 Logging in...")
    api = client.login(
        appstate=os.environ.get("APPSTATE"),
        options={
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        }
    )
    
    if not api:
        print("❌ Login failed!")
        return
    
    print("✅ Login successful!")
    
    # Test basic functionality first
    print("\n🧪 Testing basic functionality...")
    
    # Test 1: Check if send_message is available
    print("1. Testing send_message availability...")
    try:
        # Get your own user ID for testing
        your_user_id = "100093857387589"  # Replace with your actual user ID
        
        # Try to send a test message to yourself (this might fail but we'll see the error)
        test_message = "🧪 Test message from echo bot"
        api.http.send_message(test_message, your_user_id)
        print("✅ send_message works!")
    except Exception as e:
        print(f"⚠️ send_message test failed: {e}")
        print("   This is expected if you don't have a valid thread_id")
    
    # Test 2: Try MQTT listening (we know this will fail, but let's see the error)
    print("\n2. Testing MQTT listening...")
    try:
        print("🔄 Attempting to start MQTT listener...")
        api.listen_mqtt(callback)
        print("✅ MQTT listener started successfully!")
    except Exception as e:
        print(f"❌ MQTT listening failed: {e}")
        print("   This is expected due to Facebook's infrastructure changes")
    
    # Test 3: Try alternative listening methods
    print("\n3. Testing alternative approaches...")
    
    # Since MQTT doesn't work, let's try to implement a simple polling mechanism
    print("💡 MQTT doesn't work, but here's what you can do instead:")
    print("   1. Use api.http.send_message() for sending messages")
    print("   2. Implement polling to check for new messages")
    print("   3. Use webhooks if available")
    print("   4. Use the GraphQL APIs for advanced operations")
    
    # Show available functions
    print("\n📋 Available HTTP functions:")
    http_functions = [attr for attr in dir(api.http) if not attr.startswith('_')]
    for func in http_functions:
        print(f"   - api.http.{func}()")
    
    print("\n📋 Available GraphQL functions:")
    graphql_functions = [attr for attr in dir(api.graphql) if not attr.startswith('_')]
    for func in graphql_functions:
        print(f"   - api.graphql.{func}()")
    
    print("\n🎯 Echo Bot Test Complete!")
    print("📝 Summary:")
    print("   ✅ Login works")
    print("   ✅ HTTP APIs are available")
    print("   ✅ GraphQL APIs are available") 
    print("   ❌ MQTT listening doesn't work (Facebook infrastructure change)")
    print("   💡 Use HTTP APIs for sending messages instead")

if __name__ == "__main__":
    main()
