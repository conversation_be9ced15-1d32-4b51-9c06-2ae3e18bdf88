#!/usr/bin/env python3
"""
Working Bot Example - Demonstrates what actually works with the current library
"""

import os
import time
from os.path import join, dirname
from dotenv import load_dotenv
from fbapy import *

# Load environment variables
dotenv_path = join(dirname(__file__), ".env")
load_dotenv(dotenv_path)

def test_send_message(api, thread_id, message):
    """Test sending a message"""
    try:
        print(f"📤 Sending message: '{message}' to {thread_id}")
        result = api.http.send_message(message, thread_id)
        print(f"✅ Message sent successfully!")
        return True
    except Exception as e:
        print(f"❌ Failed to send message: {e}")
        return False

def test_typing_indicator(api, thread_id):
    """Test typing indicator"""
    try:
        print(f"⌨️ Setting typing indicator for {thread_id}")
        api.http.set_typing(thread_id)
        print(f"✅ Typing indicator set!")
        return True
    except Exception as e:
        print(f"❌ Failed to set typing: {e}")
        return False

def test_change_emoji(api, thread_id, emoji):
    """Test changing thread emoji"""
    try:
        print(f"😀 Changing thread emoji to {emoji} for {thread_id}")
        api.http.change_emoji(emoji, thread_id)
        print(f"✅ Emoji changed successfully!")
        return True
    except Exception as e:
        print(f"❌ Failed to change emoji: {e}")
        return False

def test_graphql_story(api, story_text):
    """Test sharing a story via GraphQL"""
    try:
        print(f"📖 Sharing story: '{story_text}'")
        api.graphql.share_story(story_text)
        print(f"✅ Story shared successfully!")
        return True
    except Exception as e:
        print(f"❌ Failed to share story: {e}")
        return False

def main():
    print("🚀 Starting Working Bot Example...")
    
    # Initialize client
    client = Client()
    
    # Login
    print("🔐 Logging in...")
    api = client.login(
        appstate=os.environ.get("APPSTATE"),
        options={
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        }
    )
    
    if not api:
        print("❌ Login failed!")
        return
    
    print("✅ Login successful!")
    
    # For testing, you'll need to replace these with actual values
    # You can get thread IDs from your browser's developer tools when viewing Facebook messages
    test_thread_id = "YOUR_THREAD_ID_HERE"  # Replace with actual thread ID
    your_user_id = "100093857387589"  # Your user ID (already extracted from login)
    
    print(f"\n👤 Your User ID: {your_user_id}")
    print(f"💬 Test Thread ID: {test_thread_id}")
    
    if test_thread_id == "YOUR_THREAD_ID_HERE":
        print("\n⚠️ WARNING: You need to replace 'YOUR_THREAD_ID_HERE' with an actual thread ID")
        print("   To get a thread ID:")
        print("   1. Go to facebook.com/messages")
        print("   2. Open a conversation")
        print("   3. Look at the URL - the number after '/t/' is the thread ID")
        print("   4. Or use browser developer tools to inspect message elements")
        print("\n🧪 Running tests with placeholder thread ID (will likely fail)...")
    
    print("\n" + "="*60)
    print("🧪 TESTING AVAILABLE FUNCTIONS")
    print("="*60)
    
    # Test 1: Send Message
    print("\n1️⃣ Testing send_message...")
    success_count = 0
    if test_send_message(api, test_thread_id, "🤖 Hello from Python Facebook API!"):
        success_count += 1
    
    # Test 2: Typing Indicator
    print("\n2️⃣ Testing typing indicator...")
    if test_typing_indicator(api, test_thread_id):
        success_count += 1
    
    # Test 3: Change Emoji
    print("\n3️⃣ Testing change emoji...")
    if test_change_emoji(api, test_thread_id, "🚀"):
        success_count += 1
    
    # Test 4: GraphQL Story
    print("\n4️⃣ Testing GraphQL story sharing...")
    if test_graphql_story(api, "Testing Python Facebook API! 🐍"):
        success_count += 1
    
    # Test 5: Alternative send_message method
    print("\n5️⃣ Testing alternative send_message...")
    try:
        print(f"📤 Sending via api.send_message...")
        api.send_message(text="🔄 Alternative send method test", thread_id=test_thread_id)
        print(f"✅ Alternative send_message works!")
        success_count += 1
    except Exception as e:
        print(f"❌ Alternative send_message failed: {e}")
    
    print("\n" + "="*60)
    print("📊 TEST RESULTS")
    print("="*60)
    print(f"✅ Successful tests: {success_count}/5")
    print(f"❌ Failed tests: {5 - success_count}/5")
    
    if success_count > 0:
        print("\n🎉 Some functions are working! The library is partially functional.")
    else:
        print("\n⚠️ All tests failed. This might be due to:")
        print("   - Invalid thread ID")
        print("   - Facebook requiring additional authentication")
        print("   - Session limitations")
    
    print("\n💡 RECOMMENDATIONS:")
    print("1. Replace 'YOUR_THREAD_ID_HERE' with a real thread ID")
    print("2. Test with a conversation you have access to")
    print("3. Use the HTTP APIs for sending messages")
    print("4. Implement polling instead of real-time listening")
    
    print("\n📝 WORKING EXAMPLE CODE:")
    print("""
# Basic message sending
api.http.send_message("Hello!", "THREAD_ID")

# Set typing indicator  
api.http.set_typing("THREAD_ID")

# Change thread emoji
api.http.change_emoji("❤️", "THREAD_ID")

# Share story
api.graphql.share_story("Hello World!")

# Alternative send method
api.send_message(text="Hello!", thread_id="THREAD_ID")
""")

if __name__ == "__main__":
    main()
